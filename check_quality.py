#!/usr/bin/env python3
"""
Local Code Quality Checker
Runs the same checks as GitHub Actions workflow locally
"""

import subprocess
import sys
import os

def load_config():
    """Load configuration from .code-quality.env file"""
    config = {}
    config_file = ".code-quality.env"
    
    if not os.path.exists(config_file):
        # Fallback to defaults if config file doesn't exist
        return {
            "FOLDERS_TO_CHECK": ["src/fhir_omop/", "scripts/", "tests/", "servers/", "setup.py", "test_manually.py"],
            "EXCLUDE_PATTERNS": "abu_dhabi_claims_mvp|temp|demo|docs/notion_references"
        }
    
    with open(config_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                config[key] = value
    
    # Convert FOLDERS_TO_CHECK from space-separated to list
    folders = config.get("FOLDERS_TO_CHECK", "").split()
    exclude_patterns = config.get("EXCLUDE_PATTERNS", "")
    
    return {
        "FOLDERS_TO_CHECK": folders,
        "EXCLUDE_PATTERNS": exclude_patterns
    }

# Load configuration from shared file
config = load_config()
FOLDERS_TO_CHECK = config["FOLDERS_TO_CHECK"]
EXCLUDE_PATTERNS = config["EXCLUDE_PATTERNS"]

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n🔍 {description}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ Tool not found. Install with: pip install {cmd[0]}")
        return False

def main():
    """Run all code quality checks"""
    print("🚀 Running local code quality checks...")
    print(f"📁 Checking: {' '.join(FOLDERS_TO_CHECK)}")
    print(f"🚫 Excluding: {EXCLUDE_PATTERNS}")
    
    all_passed = True
    
    # Black formatting check
    black_cmd = [
        "black", "--check", "--verbose",
        f"--extend-exclude=/({EXCLUDE_PATTERNS})/",
        *FOLDERS_TO_CHECK
    ]
    if not run_command(black_cmd, "Black formatting check"):
        all_passed = False
    
    # isort import sorting check
    isort_cmd = [
        "isort", "--check-only",
        f"--skip-glob=*/{EXCLUDE_PATTERNS}/*",
        *FOLDERS_TO_CHECK
    ]
    if not run_command(isort_cmd, "Import sorting check"):
        all_passed = False
    
    # flake8 style check
    flake8_cmd = [
        "flake8",
        f"--exclude=*/{EXCLUDE_PATTERNS}/*",
        *FOLDERS_TO_CHECK
    ]
    if not run_command(flake8_cmd, "Code style check"):
        all_passed = False
    
    # Final result
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All checks passed! Ready to commit.")
        sys.exit(0)
    else:
        print("💥 Some checks failed. Fix issues before committing.")
        sys.exit(1)

if __name__ == "__main__":
    main()
