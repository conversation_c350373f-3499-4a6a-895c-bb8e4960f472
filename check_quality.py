#!/usr/bin/env python3
"""
Local Code Quality Checker
Runs the same checks as GitHub Actions workflow locally
"""

import subprocess
import sys

# Paths to check - Starting with core files only (gradual approach for development repo)
PATHS_TO_CHECK = ["setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]


def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{description}...")
    print(f"Command: {' '.join(cmd)}")

    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ Tool not found. Install with: pip install {cmd[0]}")
        return False


def main():
    """Run all code quality checks"""
    print("Running local code quality checks...")
    print(f"Checking: {' '.join(PATHS_TO_CHECK)}")

    all_passed = True

    # Black formatting check
    black_cmd = ["black", "--check", "--diff"] + PATHS_TO_CHECK
    if not run_command(black_cmd, "Black formatting check"):
        all_passed = False

    # isort import sorting check
    isort_cmd = ["isort", "--check-only", "--diff"] + PATHS_TO_CHECK
    if not run_command(isort_cmd, "Import sorting check"):
        all_passed = False

    # flake8 style check
    flake8_cmd = ["flake8"] + PATHS_TO_CHECK
    if not run_command(flake8_cmd, "Code style check"):
        all_passed = False

    # Final result
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All checks passed! Ready to commit.")
        sys.exit(0)
    else:
        print("❌ Some checks failed. Fix issues before committing.")
        sys.exit(1)


if __name__ == "__main__":
    main()
