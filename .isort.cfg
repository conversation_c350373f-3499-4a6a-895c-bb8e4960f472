# isort configuration for FHIR-OMOP project
# Compatible with Black formatter and project standards

[settings]
# Black compatibility
profile = black
line_length = 88

# Import configuration
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# Import order following project standards:
# 1. Standard library
# 2. Third-party packages  
# 3. Local application imports
known_first_party = fhir_omop
known_third_party = 
    pandas,
    numpy,
    requests,
    sqlalchemy,
    pytest,
    fastapi,
    pydantic,
    fhir,
    omop

# Files and directories to skip during import sorting
# Centralized exclusion patterns (must match workflow EXCLUDE_PATTERNS)
skip = 
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    venv,
    env,
    .venv,
    .env,
    migrations,
    node_modules,
    abu_dhabi_claims_mvp,
    temp,
    demo,
    docs/notion_references

# Skip specific development modules (centralized patterns)
skip_glob = 
    */abu_dhabi_claims_mvp/*,
    */temp/*,
    */demo/*,
    */docs/notion_references/*

# Additional configurations
force_single_line = false
force_sort_within_sections = true
# Note: show_diff and color_output not supported in .cfg format
