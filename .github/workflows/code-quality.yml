# GitHub Actions Workflow for Code Quality Validation
# Progressive quality gates: focuses on stable code, excludes development templates

name: Code Quality Check

# Events that trigger the workflow
on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'scripts/**'
      - 'tests/**'
      - '*.py'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'scripts/**'
      - 'tests/**'
      - '*.py'

# Permission configuration (read-only for security)
permissions:
  contents: read

# Centralized configuration for code quality checks
env:
  # Configuration will be loaded from .code-quality.env file
  # This ensures single source of truth for all quality settings
  CONFIG_FILE: ".code-quality.env"

jobs:
  # Job to validate Python code quality
  code-quality:
    name: Python Code Quality
    runs-on: ubuntu-latest
    
    steps:
    # Step 1: Checkout repository code
    - name: Checkout repository
      uses: actions/checkout@v4
    
    # Step 2: Load configuration from .code-quality.env
    - name: Load quality configuration
      run: |
        echo "📋 Loading configuration from ${{ env.CONFIG_FILE }}..."
        
        # Function to extract value from config file
        get_config_value() {
          grep "^$1=" "${{ env.CONFIG_FILE }}" | cut -d'=' -f2
        }
        
        # Load configuration values
        FOLDERS_TO_CHECK=$(get_config_value "FOLDERS_TO_CHECK")
        EXCLUDE_PATTERNS=$(get_config_value "EXCLUDE_PATTERNS")
        BLACK_VERSION=$(get_config_value "BLACK_VERSION")
        FLAKE8_VERSION=$(get_config_value "FLAKE8_VERSION")
        ISORT_VERSION=$(get_config_value "ISORT_VERSION")
        
        # Export to GitHub environment
        echo "FOLDERS_TO_CHECK=$FOLDERS_TO_CHECK" >> $GITHUB_ENV
        echo "EXCLUDE_PATTERNS=$EXCLUDE_PATTERNS" >> $GITHUB_ENV
        echo "BLACK_VERSION=$BLACK_VERSION" >> $GITHUB_ENV
        echo "FLAKE8_VERSION=$FLAKE8_VERSION" >> $GITHUB_ENV
        echo "ISORT_VERSION=$ISORT_VERSION" >> $GITHUB_ENV
        
        # Show loaded configuration
        echo "✅ Configuration loaded successfully:"
        echo "  📁 Folders to check: $FOLDERS_TO_CHECK"
        echo "  🚫 Exclude patterns: $EXCLUDE_PATTERNS"
        echo "  🔧 Tool versions: Black=$BLACK_VERSION, flake8=$FLAKE8_VERSION, isort=$ISORT_VERSION"
    
    # Step 3: Set up Python with project version
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    
    # Step 4: Create and activate virtual environment
    - name: Create virtual environment
      run: |
        python -m venv venv
        source venv/bin/activate
        echo "VIRTUAL_ENV=venv" >> $GITHUB_ENV
        echo "$PWD/venv/bin" >> $GITHUB_PATH
    
    # Step 5: Install code quality tools (centralized versions)
    - name: Install code quality tools
      run: |
        python -m pip install --upgrade pip
        pip install black==${{ env.BLACK_VERSION }}
        pip install flake8==${{ env.FLAKE8_VERSION }}
        pip install isort==${{ env.ISORT_VERSION }}
    
    # Step 6: Validate formatting with Black (centralized control)
    - name: Check code formatting with Black
      run: |
        echo "🔍 Checking Python code formatting..."
        black --check --verbose \
          --extend-exclude="/(${{ env.EXCLUDE_PATTERNS }})/" \
          ${{ env.FOLDERS_TO_CHECK }}
      continue-on-error: false
    
    # Step 7: Validate imports with isort (centralized control)
    - name: Check import sorting with isort
      run: |
        echo "📦 Checking import organization..."
        isort --check-only \
          --skip-glob="*/${{ env.EXCLUDE_PATTERNS }}/*" \
          ${{ env.FOLDERS_TO_CHECK }}
      continue-on-error: false
    
    # Step 8: Validate style with flake8 (centralized control)
    - name: Check code style with flake8
      run: |
        echo "📝 Checking code style and linting..."
        flake8 \
          --exclude="*/${{ env.EXCLUDE_PATTERNS }}/*" \
          ${{ env.FOLDERS_TO_CHECK }}
      continue-on-error: false
    
    # Step 9: Show final summary
    - name: Quality check summary
      if: success()
      run: |
        echo "✅ All code quality checks passed!"
        echo "📊 Summary:"
        echo "  - Black formatting: ✅ Compliant"
        echo "  - Import sorting: ✅ Organized" 
        echo "  - Code style: ✅ Clean"
        echo ""
        echo "📁 Checked: ${{ env.FOLDERS_TO_CHECK }}"
        echo "🚫 Excluded: ${{ env.EXCLUDE_PATTERNS }}"
        echo ""
        echo "🎉 Your code follows FHIR-OMOP project standards!"
