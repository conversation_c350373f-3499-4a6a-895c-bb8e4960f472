name: Code Quality

on:
  push:
    branches: [main, develop]
    paths:
      - 'src/**'
      - 'scripts/**'
      - 'tests/**'
      - '*.py'
  pull_request:
    branches: [main, develop]
    paths:
      - 'src/**'
      - 'scripts/**'
      - 'tests/**'
      - '*.py'

permissions:
  contents: read

jobs:
  quality:
    name: Python Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8

    - name: Check formatting with Black
      run: black --check --diff src/ scripts/ tests/ setup.py test_manually.py

    - name: Check import sorting with isort
      run: isort --check-only --diff src/ scripts/ tests/ setup.py test_manually.py

    - name: Check code style with flake8
      run: flake8 src/ scripts/ tests/ setup.py test_manually.py
