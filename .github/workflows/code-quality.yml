name: Code Quality

on:
  push:
    branches: [main, develop]
    paths:
      - 'setup.py'
      - 'test_manually.py'
      - 'check_quality.py'
      - 'fix_quality.py'
      - '.github/workflows/code-quality.yml'
      - 'pyproject.toml'
      - '.flake8'
  pull_request:
    branches: [main, develop]
    paths:
      - 'setup.py'
      - 'test_manually.py'
      - 'check_quality.py'
      - 'fix_quality.py'
      - '.github/workflows/code-quality.yml'
      - 'pyproject.toml'
      - '.flake8'

permissions:
  contents: read

jobs:
  quality:
    name: Python Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8

    - name: Check formatting with Black
      run: black --check --diff setup.py test_manually.py check_quality.py fix_quality.py

    - name: Check import sorting with isort
      run: isort --check-only --diff setup.py test_manually.py check_quality.py fix_quality.py

    - name: Check code style with flake8
      run: flake8 setup.py test_manually.py check_quality.py fix_quality.py
