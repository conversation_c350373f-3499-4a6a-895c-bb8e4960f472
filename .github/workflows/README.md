# GitHub Actions Workflows

This directory contains automated workflows for the FHIR-OMOP project.

## � Current Workflows

### `code-quality.yml` - Code Quality Validation

**Purpose**: Automatically validates Python code quality standards.

**Triggers**:
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop`
- Only when Python files change (`src/`, `scripts/`, `tests/`, `*.py`)

**Validations**:
1. **Black formatting** - 88 character line limit
2. **Import organization** - isort with Black profile
3. **Code style** - flake8 linting

## 🔧 Local Setup

Install quality tools in your conda environment:

```bash
conda activate fhir-omop
pip install black==24.4.2 flake8==7.0.0 isort==5.13.2
```

Run checks before committing:
```bash
black --line-length 88 .
isort --profile black --line-length 88 .
flake8 .
```

## 📊 Configuration Files

| File | Purpose |
|------|---------|
| `.flake8` | Linting rules, complexity limits, exclusions |
| `.isort.cfg` | Import sorting compatible with Black |

## 🚨 Troubleshooting

**Black formatting errors**:
```bash
black --line-length 88 .  # Auto-fix
```

**Import sorting errors**:
```bash
isort --profile black --line-length 88 .  # Auto-fix
```

**Flake8 violations**:
```bash
flake8 --show-source .  # View details
```

## � Activation

After adding these files, commit and push:

```bash
git add .github/ .flake8 .isort.cfg
git commit -m "Add code quality workflow"
git push origin main
```

View workflow status in the repository's **Actions** tab.

## 📈 Future Enhancements

Potential additions:
- pytest test execution
- FHIR/OMOP data validation
- Security scanning
- Documentation generation

## 🔗 References

- [GitHub Actions Docs](https://docs.github.com/en/actions)
- [Black Formatter](https://black.readthedocs.io/)
- [isort](https://pycqa.github.io/isort/)
- [Flake8](https://flake8.pycqa.org/)
