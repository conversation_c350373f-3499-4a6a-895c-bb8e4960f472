# GitHub Actions - Code Quality

Simple, elegant code quality workflow for the FHIR-OMOP project.

## Current Implementation

### `code-quality.yml` - Gradual Code Quality Validation

**Current Scope**: Core files only (gradual approach for development repo)
- `setup.py`, `test_manually.py`, `check_quality.py`, `fix_quality.py`

**Tools**: Black, isort, flake8 with development-friendly configuration

**Triggers**: Push/PR to `main`/`develop` when relevant files change

## Local Development

### Quick Setup
```bash
conda activate fhir-omop
pip install black isort flake8
```

### Daily Workflow
```bash
# Auto-fix formatting issues
python fix_quality.py

# Check all quality standards
python check_quality.py
```

## Configuration

All configuration centralized in `pyproject.toml` and `.flake8`:
- **Line length**: 88 characters
- **Development-friendly**: Ignores common development issues
- **Black compatible**: Consistent formatting

## Gradual Expansion

Ready to expand? Update `PATHS_TO_CHECK` in scripts and workflow:

```python
# Phase 1 (current): Core files
PATHS_TO_CHECK = ["setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]

# Phase 2: Add src/
PATHS_TO_CHECK = ["src/", "setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]

# Phase 3: Add scripts/
PATHS_TO_CHECK = ["src/", "scripts/", "setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]
```

### Flake8 Error Codes Reference

For expanding quality checks, see [Flake8 Error Codes](https://flake8.pycqa.org/en/latest/user/error-codes.html):

**Useful for FHIR-OMOP project:**
- **E9**: Runtime errors (syntax issues)
- **F4**: Import errors (missing modules)
- **W6**: Deprecation warnings
- **C9**: Complexity warnings

**Currently ignored for development:**
- **F401**: Imported but unused (common in development)
- **E302/E305**: Blank line issues (auto-fixed by Black)
- **C901**: Too complex (can be enabled later)

## References

- [GitHub Actions](https://docs.github.com/en/actions)
- [Black](https://black.readthedocs.io/)
- [isort](https://pycqa.github.io/isort/)
- [Flake8 Error Codes](https://flake8.pycqa.org/en/latest/user/error-codes.html)
