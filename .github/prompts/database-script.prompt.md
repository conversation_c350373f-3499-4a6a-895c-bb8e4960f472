# Database Script Generator

Generate database scripts following our FHIR-OMOP project standards.

## Requirements

Ask for the specific database operation if not provided:
- OMOP CDM table creation
- Vocabulary loading
- Data migration
- Database setup/teardown

## Database Standards

- Use PostgreSQL 14 syntax and features
- Follow OMOP CDM v5.4.2 specifications exactly
- Use 'public' schema for OMOP CDM tables
- Implement existence detection (check before create/drop)
- Use PostgreSQL COPY method for bulk data loading

## Script Structure

```python
"""
{Script Purpose} for FHIR-OMOP Project

This script implements {operation} following OMOP CDM v5.4.2 specifications
and PostgreSQL 14 best practices.
"""

import os
import sys
import argparse
import psycopg2
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional

# Load environment variables from root .env file
load_dotenv()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="{Script description}")
    parser.add_argument(
        "--host",
        default=os.getenv("OMOP_DB_HOST", "localhost"),
        help="PostgreSQL host"
    )
    parser.add_argument(
        "--port",
        default=os.getenv("OMOP_DB_PORT", "5432"),
        help="PostgreSQL port"
    )
    parser.add_argument(
        "--database",
        default=os.getenv("OMOP_DB_NAME", "omop_abu_dhabi"),
        help="Database name"
    )
    parser.add_argument(
        "--user",
        default=os.getenv("OMOP_DB_USER", "jaimepm"),
        help="Database user"
    )
    parser.add_argument(
        "--password",
        default=os.getenv("OMOP_DB_PASSWORD", ""),
        help="Database password"
    )
    return parser.parse_args()

def check_database_exists(connection_params: dict) -> bool:
    """
    Check if database exists.
    
    Parameters
    ----------
    connection_params : dict
        Database connection parameters
        
    Returns
    -------
    bool
        True if database exists, False otherwise
    """
    # Implementation here
    pass

def main():
    """Main execution function."""
    args = parse_args()
    
    # Database connection parameters
    connection_params = {
        "host": args.host,
        "port": args.port,
        "database": args.database,
        "user": args.user,
        "password": args.password
    }
    
    try:
        # Implementation here
        print(f"✅ {operation} completed successfully")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## SQL Standards

- Use UPPERCASE for SQL keywords
- Use snake_case for table and column names
- Include proper indexing following OMOP CDM recommendations
- Add comments explaining complex operations
- Use transactions for data integrity

## Error Handling

- Implement comprehensive error handling with academic explanations
- Include rollback mechanisms for failed operations
- Provide detailed logging for debugging
- Validate data integrity after operations

## Testing

- Include dry-run mode for testing
- Validate against OMOP CDM v5.4.2 specifications
- Test with sample data before production use
- Document expected outcomes and validation steps
