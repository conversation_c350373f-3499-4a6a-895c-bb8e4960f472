# Documentation Generator

Generate documentation following our FHIR-OMOP project standards.

## Documentation Standards

- Write all documentation in English using Markdown format
- Structure as: Title → Brief Description → TOC → Main Content → References
- Use ATX-style headers with one space after # character
- Include Mermaid diagrams for complex processes and architecture
- Use inline hyperlinked citations rather than numbered references
- Include version information when referencing software or standards

## Document Structure Template

```markdown
# {Document Title}

{Brief description explaining the purpose and scope of the document}

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Implementation](#implementation)
- [Examples](#examples)
- [Troubleshooting](#troubleshooting)
- [References](#references)

## Overview

{Detailed overview with context and background}

### Technology Stack

- **OMOP CDM**: Version 5.4.2
- **HAPI FHIR**: R4 with hapiproject/hapi:latest
- **PostgreSQL**: Version 14
- **Python Environment**: Conda environment 'fhir-omop'

## Prerequisites

{List of requirements, dependencies, and setup steps}

## Implementation

{Step-by-step implementation guide with academic methodology}

### Step 1: {First Step}

{Detailed explanation with why/what/how breakdown}

```bash
# Example command
command --option value
```

### Step 2: {Second Step}

{Continue with detailed steps}

## Examples

{Practical examples with expected outputs}

## Troubleshooting

{Common issues and solutions with academic explanations}

## References

- [OMOP CDM v5.4.2 Documentation](https://ohdsi.github.io/CommonDataModel/cdm54.html)
- [HAPI FHIR R4 Documentation](https://hapifhir.io/hapi-fhir/docs/)
- [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
```

## Mermaid Diagram Standards

Use Mermaid diagrams for:
- Architecture overviews
- Process flows
- Database relationships
- ETL pipelines

Example:
```mermaid
graph TD
    A[FHIR Server] -->|Extract| B[ETL Process]
    B -->|Transform| C[OMOP Mappers]
    C -->|Load| D[OMOP Database]
    
    style A fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style D fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
```

## Academic Methodology

Apply pedagogical approach:
- Include detailed step-by-step explanations
- Provide "why/what/how" breakdowns for technical implementations
- Reference official sources with direct links and version information
- Validate against official documentation before implementation
- Include interpretation guidance for complex concepts

## File Organization

- Place documentation in appropriate directories (docs/guides/*)
- Use descriptive, lowercase names with underscores
- Update index files when adding new documentation
- Avoid creating redundant directory structures
