# OMOP Mapper Generator

Generate a new FHIR to OMOP mapper following our project standards.

## Requirements

Ask for the FHIR resource type and target OMOP table if not provided.

## Implementation Standards

- Use OMOP CDM version 5.4.2 specifications
- Follow NumPy-style docstrings with type hints
- Implement environment variable support with dotenv
- Include comprehensive error handling with academic explanations
- Reference the [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)

## Code Structure

```python
"""
FHIR {resource_type} to OMOP {table_name} Mapper

This module implements the transformation of FHIR {resource_type} resources
to OMOP CDM v5.4.2 {table_name} table following the HL7 Vulcan Implementation Guide.
"""

import os
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class {ResourceType}To{TableName}Mapper:
    """
    Maps FHIR {resource_type} resources to OMOP CDM {table_name} table.
    
    This mapper follows the HL7 Vulcan FHIR-to-OMOP Implementation Guide
    and implements the official OMOP CDM v5.4.2 specifications.
    """
    
    def __init__(self, vocabulary_service: Optional[Any] = None):
        """
        Initialize the mapper with vocabulary service.
        
        Parameters
        ----------
        vocabulary_service : Optional[Any]
            Service for concept mapping and vocabulary lookups
        """
        self.vocabulary_service = vocabulary_service
    
    def map_resource(self, fhir_resource: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform FHIR {resource_type} to OMOP {table_name}.
        
        Parameters
        ----------
        fhir_resource : Dict[str, Any]
            FHIR {resource_type} resource as dictionary
            
        Returns
        -------
        Dict[str, Any]
            OMOP {table_name} record following CDM v5.4.2 specifications
            
        Raises
        ------
        ValueError
            If required FHIR fields are missing
        KeyError
            If vocabulary mapping fails
        """
        # Implementation here
        pass
```

## Validation Requirements

- Validate against OMOP CDM v5.4.2 table specifications
- Include data quality checks with 5% tolerance
- Implement comprehensive logging for debugging
- Add unit tests using pytest with academic test descriptions

## Documentation

- Include detailed docstrings explaining the mapping logic
- Reference official OMOP CDM documentation
- Provide examples of input FHIR resources and output OMOP records
- Document any assumptions or limitations
