#!/usr/bin/env python3
"""
Auto-fix Code Quality Issues
Automatically applies Black and isort fixes
"""

import subprocess
import sys
import os

def load_config():
    """Load configuration from .code-quality.env file"""
    config = {}
    config_file = ".code-quality.env"
    
    if not os.path.exists(config_file):
        # Fallback to defaults if config file doesn't exist
        return {
            "FOLDERS_TO_CHECK": ["src/fhir_omop/", "scripts/", "tests/", "servers/", "setup.py", "test_manually.py"],
            "EXCLUDE_PATTERNS": "abu_dhabi_claims_mvp|temp|demo|docs/notion_references"
        }
    
    with open(config_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                config[key] = value
    
    # Convert FOLDERS_TO_CHECK from space-separated to list
    folders = config.get("FOLDERS_TO_CHECK", "").split()
    exclude_patterns = config.get("EXCLUDE_PATTERNS", "")
    
    return {
        "FOLDERS_TO_CHECK": folders,
        "EXCLUDE_PATTERNS": exclude_patterns
    }

# Load configuration from shared file
config = load_config()
FOLDERS_TO_CHECK = config["FOLDERS_TO_CHECK"]
EXCLUDE_PATTERNS = config["EXCLUDE_PATTERNS"]

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n🔧 {description}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ APPLIED")
        if result.stdout:
            print("Output:", result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ Tool not found. Install with: pip install {cmd[0]}")
        return False

def main():
    """Auto-fix code quality issues"""
    print("🛠️  Auto-fixing code quality issues...")
    print(f"📁 Processing: {' '.join(FOLDERS_TO_CHECK)}")
    print(f"🚫 Excluding: {EXCLUDE_PATTERNS}")
    
    # Black auto-format
    black_cmd = [
        "black",
        f"--extend-exclude=/({EXCLUDE_PATTERNS})/",
        *FOLDERS_TO_CHECK
    ]
    run_command(black_cmd, "Auto-formatting with Black")
    
    # isort auto-fix
    isort_cmd = [
        "isort",
        f"--skip-glob=*/{EXCLUDE_PATTERNS}/*",
        *FOLDERS_TO_CHECK
    ]
    run_command(isort_cmd, "Auto-organizing imports with isort")
    
    print("\n" + "="*50)
    print("🎉 Auto-fixes applied! Run 'python check_quality.py' to verify.")

if __name__ == "__main__":
    main()
