#!/usr/bin/env python3
"""
Auto-fix Code Quality Issues
Automatically applies Black and isort fixes
"""

import subprocess
import sys

# Paths to check - Starting with core files only (gradual approach for development repo)
PATHS_TO_CHECK = ["setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]


def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{description}...")
    print(f"Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ APPLIED")
        if result.stdout:
            print(
                "Output:",
                (
                    result.stdout[:200] + "..."
                    if len(result.stdout) > 200
                    else result.stdout
                ),
            )
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED")
        print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ Tool not found. Install with: pip install {cmd[0]}")
        return False


def main():
    """Auto-fix code quality issues"""
    print("Auto-fixing code quality issues...")
    print(f"Processing: {' '.join(PATHS_TO_CHECK)}")

    # Black auto-format
    black_cmd = ["black"] + PATHS_TO_CHECK
    run_command(black_cmd, "Auto-formatting with Black")

    # isort auto-fix
    isort_cmd = ["isort"] + PATHS_TO_CHECK
    run_command(isort_cmd, "Auto-organizing imports with isort")

    print("\n" + "=" * 50)
    print("✅ Auto-fixes applied! Run 'python check_quality.py' to verify.")


if __name__ == "__main__":
    main()
