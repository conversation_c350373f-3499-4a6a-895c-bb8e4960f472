{
  "folders": [
    {
      "name": "FHIR-OMOP Main",
      "path": "."
    }
  ],
  "settings": {
    // =================================================================
    // GITHUB COPILOT WORKSPACE CONFIGURATION
    // =================================================================
    
    // Enable Copilot for all relevant file types
    "github.copilot.enable": {
      "*": true,
      "yaml": true,
      "plaintext": true,
      "markdown": true,
      "python": true,
      "sql": true,
      "dockerfile": true,
      "shellscript": true,
      "json": true
    },

    // Global Workspace Custom Instructions
    "github.copilot.chat.codeGeneration.instructions": [
      {
        "file": ".github/copilot-instructions.md"
      },
      {
        "file": "docs/guides/development/standards.md"
      }
    ],

    // Workspace-specific commit message generation
    "github.copilot.chat.commitMessageGeneration.instructions": [
      {
        "text": "Generate commit messages for FHIR-OMOP project. Include component (FHIR/OMOP/ETL/docs), describe technical changes using imperative mood with capital first letter. Reference OMOP CDM v5.4.2 or HAPI FHIR R4 when relevant. Follow academic methodology with detailed explanations."
      }
    ],

    // Enable prompt files for reusable prompts
    "chat.promptFiles": true,

    // =================================================================
    // PROJECT-SPECIFIC SETTINGS
    // =================================================================
    
    // Python configuration for FHIR-OMOP project
    "python.defaultInterpreterPath": "/Users/<USER>/miniconda3/envs/fhir-omop/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.condaPath": "/Users/<USER>/miniconda3/bin/conda",
    "python.analysis.extraPaths": [
      "${workspaceFolder}/src"
    ],

    // Editor configuration
    "editor.rulers": [88],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    },

    // Language-specific settings
    "[python]": {
      "editor.defaultFormatter": "ms-python.black-formatter",
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
      },
      "editor.rulers": [88]
    },

    "[markdown]": {
      "editor.wordWrap": "on",
      "editor.quickSuggestions": {
        "comments": "off",
        "strings": "off", 
        "other": "off"
      }
    },

    "[sql]": {
      "editor.defaultFormatter": "ms-mssql.mssql"
    },

    // File associations
    "files.associations": {
      "*.md": "markdown",
      "*.sql": "sql",
      "*.yml": "yaml",
      "*.yaml": "yaml",
      ".env*": "properties",
      "copilot-instructions.md": "markdown",
      "*.prompt.md": "markdown"
    },

    // File exclusions for security
    "files.exclude": {
      "**/.env": true,
      "**/.env.local": true,
      "**/.env.*.local": true,
      "**/node_modules": true,
      "**/__pycache__": true,
      "**/.pytest_cache": true,
      "**/venv": true,
      "**/env": true
    },

    // Search exclusions
    "search.exclude": {
      "**/node_modules": true,
      "**/bower_components": true,
      "**/*.code-search": true,
      "**/venv": true,
      "**/__pycache__": true,
      "**/.pytest_cache": true
    },

    // Terminal configuration
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.env.osx": {
      "CONDA_DEFAULT_ENV": "fhir-omop"
    },

    // Git configuration
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,

    // Workspace appearance
    "workbench.editor.labelFormat": "medium",
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false
  },
  
  "extensions": {
    "recommendations": [
      // GitHub Copilot
      "github.copilot",
      "github.copilot-chat",
      
      // Python Development
      "ms-python.python",
      "ms-python.black-formatter",
      "ms-python.flake8",
      "ms-python.isort",
      
      // Database Tools
      "ms-mssql.mssql",
      "cweijan.vscode-postgresql-client2",
      
      // Docker
      "ms-azuretools.vscode-docker",
      
      // Markdown and Documentation
      "bierner.markdown-mermaid",
      "yzhang.markdown-all-in-one",
      "davidanson.vscode-markdownlint",
      
      // YAML and Configuration
      "redhat.vscode-yaml",
      
      // Git and Version Control
      "eamodio.gitlens",
      
      // Jupyter Notebooks
      "ms-toolsai.jupyter",
      
      // Data Science
      "ms-toolsai.datawrangler"
    ]
  },
  
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Python: Current File",
        "type": "python",
        "request": "launch",
        "program": "${file}",
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "env": {
          "PYTHONPATH": "${workspaceFolder}/src"
        }
      },
      {
        "name": "Python: FHIR ETL Pipeline",
        "type": "python",
        "request": "launch",
        "program": "${workspaceFolder}/src/fhir_omop/main.py",
        "console": "integratedTerminal",
        "cwd": "${workspaceFolder}",
        "env": {
          "PYTHONPATH": "${workspaceFolder}/src"
        }
      }
    ]
  },
  
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Run Tests",
        "type": "shell",
        "command": "conda",
        "args": ["run", "-n", "fhir-omop", "pytest"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        }
      },
      {
        "label": "Start FHIR Server",
        "type": "shell",
        "command": "./manage-fhir-server.sh",
        "args": ["start", "postgres"],
        "options": {
          "cwd": "${workspaceFolder}/servers/fhir-server"
        },
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        }
      }
    ]
  }
}
