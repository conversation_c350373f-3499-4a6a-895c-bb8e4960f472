# Flake8 configuration for FHIR-OMOP project
# Aligned with project standards: 88 character line length, Black compatible

[flake8]
# Maximum line length (Black compatible)
max-line-length = 88

# Ignore errors that <PERSON> handles automatically
extend-ignore = 
    E203,
    W503,
    E501

# Files and directories to exclude from flake8 checks
# Centralized exclusion patterns (must match workflow EXCLUDE_PATTERNS)
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    venv,
    env,
    .venv,
    .env,
    .pytest_cache,
    migrations,
    node_modules,
    abu_dhabi_claims_mvp,
    temp,
    demo,
    docs/notion_references

# Additional configurations
select = E,W,F,C
per-file-ignores =
    # Allow long imports in __init__.py
    __init__.py:F401,E402
    # Allow long lines in configuration files
    setup.py:E501
    # Allow unused imports in migration files
    */migrations/*.py:F401

# Configure maximum cyclomatic complexity
max-complexity = 10

# Configure import line style
import-order-style = google
application-import-names = fhir_omop
