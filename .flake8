# Flake8 configuration for FHIR-OMOP project
# Black compatible configuration

[flake8]
max-line-length = 88

# Ignore errors that <PERSON> handles automatically + development-friendly ignores
extend-ignore =
    E203,
    W503,
    E501,
    F401,
    W293,
    E302,
    E305,
    C901

# Files and directories to exclude
exclude =
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info,
    venv,
    env,
    .venv,
    .pytest_cache,
    temp,
    demo,
    docs/notion_references

# Additional configurations
select = E,W,F,C
max-complexity = 10

# Per-file ignores
per-file-ignores =
    __init__.py:F401,E402
    setup.py:E501
