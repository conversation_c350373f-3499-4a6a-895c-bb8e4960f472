# Code Quality Configuration
# Single source of truth for all quality tools

# Folders and files to check (space-separated for GitHub Actions)
FOLDERS_TO_CHECK=src/fhir_omop/ scripts/ tests/ servers/ setup.py test_manually.py

# Patterns to exclude (pipe-separated for regex)
EXCLUDE_PATTERNS=abu_dhabi_claims_mvp|temp|demo|docs/notion_references

# Tool versions
BLACK_VERSION=24.4.2
FLAKE8_VERSION=7.0.0
ISORT_VERSION=5.13.2
