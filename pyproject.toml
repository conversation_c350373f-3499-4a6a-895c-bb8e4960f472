# FHIR-OMOP Project Configuration
# This file centralizes configuration for modern Python development tools
#
# CONFIGURATION STRATEGY (2024):
# - Modern tools (Black, isort, pytest, coverage) → pyproject.toml (native support)
# - Legacy tools (flake8) → separate config files (until native support available)
# - This hybrid approach ensures maximum compatibility and future readiness

[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "fhir-omop"
description = "FHIR to OMOP CDM Transformation Pipeline"
readme = "README.md"
requires-python = ">=3.10"
keywords = ["fhir", "omop", "healthcare", "etl"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Healthcare Industry",
    "Programming Language :: Python :: 3.10",
]

# ================================================================
# CODE QUALITY TOOLS CONFIGURATION
# ================================================================

[tool.black]
line-length = 88
target-version = ["py310"]
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | build
  | dist
  | venv
  | env
  | __pycache__
  | temp
  | demo
  | docs/notion_references
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
force_sort_within_sections = true

# Project-specific import organization
known_first_party = ["fhir_omop"]
known_third_party = [
    "pandas",
    "numpy",
    "requests",
    "sqlalchemy",
    "pytest",
    "fastapi",
    "pydantic",
    "fhir",
    "omop"
]

# Files to skip
skip = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
    "venv",
    "env",
    ".venv",
    "temp",
    "demo",
    "docs/notion_references"
]

# ================================================================
# TESTING CONFIGURATION (Ready for Phase 2)
# ================================================================

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--strict-config",
    "--tb=short",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",  
    "fhir: FHIR-related tests",
    "omop: OMOP CDM-related tests",
    "etl: ETL process tests",
    "slow: Slow-running tests",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# ================================================================
# COVERAGE CONFIGURATION (Ready for future)
# ================================================================

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*", 
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError", 
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
