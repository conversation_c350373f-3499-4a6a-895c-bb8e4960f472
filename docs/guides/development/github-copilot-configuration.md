# GitHub Copilot Configuration Guide for FHIR-OMOP Project

This guide explains how to configure and use GitHub Copilot effectively in our FHIR-OMOP project, including workspace-specific settings and reusable prompt files.

## Table of Contents

- [Overview](#overview)
- [Configuration Files](#configuration-files)
- [Workspace Setup](#workspace-setup)
- [Using Custom Instructions](#using-custom-instructions)
- [Prompt Files Usage](#prompt-files-usage)
- [Multi-Workspace Configuration](#multi-workspace-configuration)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

Our GitHub Copilot configuration provides:

- **Repository Custom Instructions**: Project-specific context and standards
- **Workspace Settings**: VSCode-specific configurations for optimal development
- **Prompt Files**: Reusable prompts for common tasks
- **Multi-Workspace Support**: Consistent settings across different project configurations

### Technology Stack Integration

- **OMOP CDM**: Version 5.4.2 specifications automatically referenced
- **HAPI FHIR**: R4 standards and best practices included
- **PostgreSQL**: Version 14 compatibility and patterns
- **Python Environment**: Conda 'fhir-omop' environment integration

## Configuration Files

### 1. Repository Custom Instructions

**File**: `.github/copilot-instructions.md`

Contains project-specific instructions that GitHub Copilot uses automatically:

- Technology stack specifications (OMOP CDM v5.4.2, HAPI FHIR R4, PostgreSQL 14)
- Code standards and patterns (NumPy docstrings, type hints, environment variables)
- Development methodology (academic approach, step-by-step explanations)
- Project-specific architecture patterns

### 2. Workspace Settings

**File**: `.vscode/settings.json`

VSCode workspace-specific configurations:

```json
{
  "github.copilot.chat.codeGeneration.instructions": [
    {
      "file": ".github/copilot-instructions.md"
    },
    {
      "file": "docs/guides/development/standards.md"
    }
  ]
}
```

### 3. Prompt Files

**Directory**: `.github/prompts/`

Reusable prompt templates for common tasks:

- `omop-mapper.prompt.md` - Generate FHIR to OMOP mappers
- `database-script.prompt.md` - Create database scripts
- `documentation.prompt.md` - Generate project documentation

## Workspace Setup

### Single Workspace Setup

1. **Open the project in VSCode**:
   ```bash
   code /path/to/fhir-omop
   ```

2. **Verify Copilot is enabled**:
   - Check status bar for Copilot icon
   - Ensure `.github/copilot-instructions.md` is detected

3. **Test custom instructions**:
   - Open Copilot Chat
   - Type a simple prompt like "create omop mapper"
   - Verify it follows project standards

### Multi-Workspace Setup

1. **Open the workspace file**:
   ```bash
   code fhir-omop.code-workspace
   ```

2. **Benefits of workspace file**:
   - Consistent settings across team members
   - Pre-configured launch configurations
   - Recommended extensions
   - Project-specific tasks

## Using Custom Instructions

### Automatic Application

Custom instructions are automatically applied to:

- **Copilot Chat**: All chat interactions
- **Code Generation**: Inline suggestions and completions
- **Code Review**: When reviewing pull requests
- **Commit Messages**: Generated commit messages

### Verification

Check if custom instructions are active:

1. Open Copilot Chat
2. Ask a technical question
3. Look for "References" section in response
4. Verify `.github/copilot-instructions.md` is listed

### Example Usage

```
User: "Create a patient mapper"

Copilot Response: (with custom instructions)
- Uses OMOP CDM v5.4.2 specifications
- Includes NumPy-style docstrings
- Implements environment variable support
- Follows academic methodology
- References HL7 Vulcan Implementation Guide
```

## Prompt Files Usage

### Enabling Prompt Files

Ensure this setting is in your workspace:

```json
{
  "chat.promptFiles": true
}
```

### Using Prompt Files

1. **Access prompt files**:
   - Open Copilot Chat
   - Click the attach button (📎)
   - Select "Prompts..."
   - Choose the appropriate prompt file

2. **Available prompt files**:
   - **OMOP Mapper**: Generate FHIR to OMOP transformation code
   - **Database Script**: Create database operation scripts
   - **Documentation**: Generate project documentation

### Example: Creating an OMOP Mapper

1. Open Copilot Chat
2. Attach `omop-mapper.prompt.md`
3. Specify: "Patient to Person mapper"
4. Copilot generates complete mapper following project standards

## Multi-Workspace Configuration

### Workspace File Benefits

The `fhir-omop.code-workspace` file provides:

- **Consistent Settings**: Same configuration across team members
- **Extension Recommendations**: Automatically suggests required extensions
- **Launch Configurations**: Pre-configured debugging setups
- **Tasks**: Common project tasks (tests, FHIR server startup)

### Adding Additional Projects

To add related projects to the workspace:

```json
{
  "folders": [
    {
      "name": "FHIR-OMOP Main",
      "path": "."
    },
    {
      "name": "Related Project",
      "path": "../related-project"
    }
  ]
}
```

## Best Practices

### 1. Custom Instructions

- **Keep instructions specific**: Focus on project-specific patterns
- **Reference official documentation**: Include version numbers
- **Update regularly**: Keep instructions current with project evolution
- **Test effectiveness**: Verify Copilot follows instructions

### 2. Prompt Files

- **Create reusable templates**: For common development tasks
- **Include context**: Reference related files and documentation
- **Maintain consistency**: Follow project naming conventions
- **Document usage**: Explain when to use each prompt file

### 3. Workspace Settings

- **Version control**: Include workspace settings in repository
- **Team consistency**: Ensure all team members use same settings
- **Security**: Exclude sensitive files from Copilot
- **Performance**: Configure appropriate file exclusions

### 4. Multi-Project Workflows

- **Organize logically**: Group related projects in workspaces
- **Shared settings**: Use workspace-level settings for common configurations
- **Project-specific overrides**: Use folder-level settings when needed

## Troubleshooting

### Custom Instructions Not Working

1. **Check file location**: Ensure `.github/copilot-instructions.md` exists
2. **Verify settings**: Check workspace settings reference the file
3. **Restart VSCode**: Reload window to refresh Copilot
4. **Check references**: Look for file in Copilot Chat references

### Prompt Files Not Available

1. **Enable feature**: Set `"chat.promptFiles": true` in settings
2. **Check directory**: Ensure `.github/prompts/` directory exists
3. **File naming**: Verify files end with `.prompt.md`
4. **Restart required**: Reload VSCode after enabling

### Workspace Settings Issues

1. **JSON syntax**: Validate settings.json syntax
2. **Experimental features**: Some settings are preview features
3. **Extension conflicts**: Check for conflicting extensions
4. **User vs workspace**: Understand setting precedence

### Performance Issues

1. **File exclusions**: Add large directories to exclusions
2. **Copilot scope**: Limit Copilot to relevant file types
3. **Workspace size**: Consider splitting large workspaces
4. **Extension management**: Disable unnecessary extensions

## Replicating This Methodology in Other Projects

### Step-by-Step Replication Guide

#### 1. Create Repository Custom Instructions

Create `.github/copilot-instructions.md` with:

```markdown
# GitHub Copilot Instructions for [Project Name]

## Technology Stack Specifications
- List specific versions and technologies
- Include compatibility requirements
- Specify development environment

## Code Standards and Patterns
- Define coding conventions
- Specify documentation standards
- Include architectural patterns

## Development Methodology
- Define development approach
- Include quality standards
- Specify testing requirements

## Project-Specific Architecture
- Reference implementation guides
- Define project patterns
- Include domain-specific requirements
```

#### 2. Configure Workspace Settings

Add to `.vscode/settings.json`:

```json
{
  "github.copilot.chat.codeGeneration.instructions": [
    {
      "file": ".github/copilot-instructions.md"
    }
  ],
  "github.copilot.chat.commitMessageGeneration.instructions": [
    {
      "text": "Project-specific commit message guidelines"
    }
  ],
  "chat.promptFiles": true
}
```

#### 3. Create Project-Specific Prompt Files

In `.github/prompts/`:
- `component-generator.prompt.md` - For main project components
- `test-generator.prompt.md` - For testing patterns
- `documentation.prompt.md` - For documentation standards

#### 4. Setup Multi-Workspace Configuration

Create `[project-name].code-workspace`:
- Include project-specific settings
- Add recommended extensions
- Configure launch configurations
- Define common tasks

### Adaptation Guidelines

#### For Different Technology Stacks

**Web Development Projects**:
```markdown
## Technology Stack Specifications
- React/Vue/Angular version
- Node.js version
- Database technology
- Deployment platform
```

**Data Science Projects**:
```markdown
## Technology Stack Specifications
- Python version and key libraries
- Jupyter notebook standards
- Data processing frameworks
- Model deployment requirements
```

**Mobile Development Projects**:
```markdown
## Technology Stack Specifications
- Platform versions (iOS/Android)
- Development frameworks
- Testing frameworks
- Deployment processes
```

#### For Different Team Sizes

**Small Teams (2-5 developers)**:
- Focus on essential standards
- Simplified prompt files
- Basic workspace configuration

**Large Teams (10+ developers)**:
- Comprehensive guidelines
- Detailed prompt files
- Advanced workspace configurations
- Multiple workspace files for different roles

### Maintenance and Evolution

#### Regular Updates

1. **Monthly Reviews**: Update technology versions
2. **Quarterly Assessments**: Evaluate effectiveness
3. **Project Milestones**: Adapt to new requirements
4. **Team Feedback**: Incorporate developer suggestions

#### Version Control

- Track changes to custom instructions
- Document reasoning for modifications
- Maintain backward compatibility when possible
- Communicate changes to team members

## References

- [GitHub Copilot Custom Instructions Documentation](https://docs.github.com/en/copilot/how-tos/custom-instructions/adding-repository-custom-instructions-for-github-copilot)
- [VSCode Multi-root Workspaces](https://code.visualstudio.com/docs/editing/workspaces/multi-root-workspaces)
- [VSCode Custom Instructions Blog](https://code.visualstudio.com/blogs/2025/03/26/custom-instructions)
- [OMOP CDM v5.4.2 Documentation](https://ohdsi.github.io/CommonDataModel/cdm54.html)
- [HAPI FHIR R4 Documentation](https://hapifhir.io/hapi-fhir/docs/)
