# Code Quality System Documentation

## Overview

This document describes the centralized code quality system for the FHIR-OMOP project. The system provides consistent code formatting, import organization, and style checking across local development and CI/CD pipelines.

## Architecture

### Single Source of Truth
All configuration is centralized in `.code-quality.env` file:
- ✅ **Centralized**: One place to update all quality settings
- ✅ **Consistent**: Same configuration for local scripts and CI
- ✅ **Maintainable**: Easy to update tool versions and settings

### Components

1. **Configuration File**: `.code-quality.env` - Central configuration
2. **Local Scripts**: `check_quality.py` and `fix_quality.py` - Development tools
3. **CI Workflow**: `.github/workflows/code-quality.yml` - Automated validation
4. **Tool Configurations**: `.flake8`, `.isort.cfg`, `pyproject.toml` - Tool-specific settings

## Usage

### Local Development

#### Check Code Quality
```bash
# Check all configured files and folders
python check_quality.py
```

#### Auto-Fix Issues
```bash
# Automatically fix formatting and import issues
python fix_quality.py
```

### CI/CD Pipeline

The GitHub Actions workflow automatically:
1. Loads configuration from `.code-quality.env`
2. Validates code formatting with Black
3. Checks import organization with isort
4. Performs style checking with flake8

## Configuration

### Adding/Removing Folders

Edit `.code-quality.env`:
```env
# Add or remove folders (space-separated)
FOLDERS_TO_CHECK=src/fhir_omop/ scripts/ tests/ servers/ new_folder/

# Add exclusion patterns (pipe-separated for regex)
EXCLUDE_PATTERNS=abu_dhabi_claims_mvp|temp|demo|docs/notion_references|new_exclude_pattern
```

### Updating Tool Versions

Edit `.code-quality.env`:
```env
# Update tool versions
BLACK_VERSION=24.4.2
FLAKE8_VERSION=7.0.0
ISORT_VERSION=5.13.2
```

## Tools Configuration

### Black (Code Formatting)
- **Line Length**: 88 characters
- **Target Version**: Python 3.10
- **Configuration**: `pyproject.toml` → `[tool.black]`

### isort (Import Organization)
- **Profile**: Black-compatible
- **Configuration**: `pyproject.toml` → `[tool.isort]`

### flake8 (Style Checking)
- **Line Length**: 88 characters
- **Configuration**: `.flake8` file

## Development Workflow

### Before Committing
1. Run `python fix_quality.py` to auto-fix formatting issues
2. Run `python check_quality.py` to validate all checks pass
3. Commit your changes
4. CI will automatically validate on push/PR

### Adding New Code
1. Write your code
2. Run `python fix_quality.py` to ensure proper formatting
3. Address any flake8 issues that can't be auto-fixed
4. Commit with confidence

## Troubleshooting

### Common Issues

#### "No module named 'black'"
```bash
# Install quality tools
pip install black==24.4.2 flake8==7.0.0 isort==5.13.2
```

#### "Files would be reformatted"
```bash
# Auto-fix formatting issues
python fix_quality.py
```

#### "Import sorting issues"
```bash
# Auto-fix import organization
python fix_quality.py
```

### CI Failures

If the CI workflow fails:
1. Run `python check_quality.py` locally
2. Fix any issues shown
3. Push your changes
4. CI will re-run automatically

## Technical Details

### CI Implementation
The GitHub Actions workflow:
1. Loads configuration from `.code-quality.env` using shell commands
2. Exports variables to `$GITHUB_ENV` for use in subsequent steps
3. Uses the same exclusion patterns as local scripts

### Configuration Loading
```bash
# Function to extract values from config file
get_config_value() {
  grep "^$1=" ".code-quality.env" | cut -d'=' -f2
}
```

### Environment Variables
- `FOLDERS_TO_CHECK`: Space-separated list of folders/files to check
- `EXCLUDE_PATTERNS`: Pipe-separated regex patterns for exclusions
- `BLACK_VERSION`, `FLAKE8_VERSION`, `ISORT_VERSION`: Tool versions

## Benefits

### For Developers
- ✅ **Consistent**: Same formatting standards across the team
- ✅ **Automated**: Auto-fix most issues with one command
- ✅ **Fast**: Local validation before CI
- ✅ **Simple**: Easy to understand and use

### For Project
- ✅ **Maintainable**: Single source of truth for all settings
- ✅ **Scalable**: Easy to add new folders or update configurations
- ✅ **Reliable**: Consistent behavior between local and CI environments
- ✅ **Professional**: Clean, well-formatted codebase

## Future Enhancements

### Planned Features
- [ ] Integration with pre-commit hooks
- [ ] Code coverage reporting
- [ ] Documentation generation validation
- [ ] Advanced linting rules for domain-specific patterns

### Extension Points
- Additional tools can be added by updating `.code-quality.env`
- New exclusion patterns can be added without changing code
- Tool versions can be updated centrally

---

**Last Updated**: July 2025  
**Version**: 1.0  
**Author**: FHIR-OMOP Development Team
