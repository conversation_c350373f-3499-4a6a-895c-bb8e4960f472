# Code Quality System

Simple, elegant code quality system for the FHIR-OMOP project with gradual expansion approach.

## Quick Start

### Daily Development
```bash
# Auto-fix formatting and imports
python fix_quality.py

# Check all quality standards
python check_quality.py
```

### First-Time Setup
```bash
conda activate fhir-omop
# Verify Python version (should be 3.11+)
python --version
pip install black isort flake8
```

**Important**: flake8 must run on the same Python version as your code to properly understand language features. Our project uses Python 3.11.

## Current Implementation

### Gradual Approach
**Phase 1 (Current)**: Core files only
- `setup.py`, `test_manually.py`, `check_quality.py`, `fix_quality.py`
- Development-friendly configuration (ignores common development issues)

**Why gradual?** This is a development repository with many draft scripts. We start with core files and expand gradually.

### Tools
- **Black**: Code formatting (88 characters)
- **isort**: Import organization (Black-compatible)
- **flake8**: Style checking (permissive for development)

## Configuration

### Python Version Consistency
**Critical**: All environments must use the same Python version for consistent results.

- **Local environment**: Python 3.11 (conda fhir-omop)
- **GitHub Actions**: Python 3.11
- **Black target**: py311

**Why?** flake8 is tied to the Python version it runs on and needs to understand the same language features as your code.

### Centralized in `pyproject.toml`
```toml
[tool.black]
line-length = 88
target-version = ["py310"]

[tool.isort]
profile = "black"
line_length = 88
```

### Development-Friendly `.flake8`
```ini
[flake8]
max-line-length = 88
extend-ignore =
    E203, W503, E501,  # Black compatibility
    F401, W293,        # Development-friendly
    E302, E305, C901   # Common development issues
```

## Expansion Strategy

### Phase 2: Add Source Code
```python
# Update PATHS_TO_CHECK in both scripts and workflow
PATHS_TO_CHECK = ["src/", "setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]
```

### Phase 3: Add Scripts
```python
PATHS_TO_CHECK = ["src/", "scripts/", "setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]
```

### Phase 4: Add Tests
```python
PATHS_TO_CHECK = ["src/", "scripts/", "tests/", "setup.py", "test_manually.py", "check_quality.py", "fix_quality.py"]
```

## Flake8 Error Codes for FHIR-OMOP

### Currently Ignored (Development Phase)
- **F401**: Imported but unused (common during development)
- **W293**: Blank line contains whitespace
- **E302/E305**: Expected blank lines (auto-fixed by Black)
- **C901**: Too complex (can enable later for code quality)

### Useful for Future Phases
- **E9**: Runtime errors (syntax issues) - Always important
- **F4**: Import errors (missing modules) - Critical for dependencies
- **W6**: Deprecation warnings - Important for FHIR/OMOP libraries
- **N8**: Naming conventions - Good for professional code

### FHIR-OMOP Specific Considerations
- **F811**: Redefined unused name - Important for FHIR resource handling
- **F841**: Local variable assigned but never used - Common in ETL scripts
- **W291**: Trailing whitespace - Keep enabled for clean commits

**Reference**: [Flake8 Error Codes Documentation](https://flake8.pycqa.org/en/latest/user/error-codes.html)

## GitHub Actions Integration

### Automatic Validation
- Triggers on push/PR to `main`/`develop`
- Only runs when relevant files change
- Uses same configuration as local scripts

### Local-CI Consistency
```bash
# What runs locally...
python check_quality.py

# ...is exactly what runs in CI
# No configuration drift!
```

## Troubleshooting

### Common Issues
```bash
# Tool not found
pip install black isort flake8

# Formatting issues
python fix_quality.py

# Check what will be validated
python check_quality.py
```

### Expanding Too Fast?
If you get too many errors when expanding:
1. Run `python fix_quality.py` first
2. Address remaining flake8 issues gradually
3. Consider temporarily ignoring specific error codes
4. Expand one directory at a time

## Benefits

- **Simple**: Minimal configuration, maximum effectiveness
- **Gradual**: Expand at your own pace
- **Consistent**: Same standards locally and in CI
- **Development-friendly**: Doesn't block development workflow

---

**Implementation**: Gradual expansion approach for development repositories
**Last Updated**: January 2025
