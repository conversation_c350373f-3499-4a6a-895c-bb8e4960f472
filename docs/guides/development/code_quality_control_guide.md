# Control de código para análisis de calidad - Guía práctica

## 1. 🎯 Control a nivel de Workflow (GitHub Actions)

### Método A: Especificar archivos exactos
```bash
# Analizar solo archivos específicos
black --check \
  src/fhir_omop/config.py \
  src/fhir_omop/main.py \
  scripts/load_vocabularies.py

# Analizar carpetas específicas
black --check \
  src/fhir_omop/mappers/ \
  src/fhir_omop/utils/ \
  tests/
```

### Método B: Usar patrones de exclusión
```bash
# Excluir patrones específicos
black --check --exclude="/(temp|demo|.*_mvp)/" .

# Excluir múltiples patrones
black --check \
  --extend-exclude="/(abu_dhabi_claims_mvp|temp|demo|docs\/notion_references)/" \
  src/fhir_omop/
```

### Método C: Usar find para control avanzado
```bash
# Analizar solo archivos modificados recientemente
find src/ -name "*.py" -mtime -7 -exec black --check {} \;

# Analizar solo archivos que no contienen "template" en el nombre
find src/ -name "*.py" ! -name "*template*" -exec black --check {} \;
```

## 2. 🛠️ Control por archivos de configuración

### pyproject.toml (Black e isort)
```toml
[tool.black]
# Incluir solo archivos específicos
include = '''
/(
    src/fhir_omop/(config|main|mappers|utils)
  | tests/
  | scripts/load_vocabularies.py
)\.py$
'''

# O excluir patrones específicos
extend-exclude = '''
/(
    abu_dhabi_claims_mvp
  | temp
  | demo
  | .*_template
  | .*_mvp
)/
'''
```

### .flake8 (flake8)
```ini
[flake8]
# Incluir solo archivos específicos
filename = 
    src/fhir_omop/config.py,
    src/fhir_omop/main.py,
    src/fhir_omop/mappers/*.py,
    tests/*.py

# O excluir patrones
exclude = 
    */abu_dhabi_claims_mvp/*,
    */temp/*,
    */demo/*,
    *template*,
    *_mvp.py
```

## 3. 📁 Control por estructura de carpetas

### Opción A: Crear carpetas separadas
```
src/fhir_omop/
├── stable/          # Código listo para validación
│   ├── mappers/
│   ├── utils/
│   └── config.py
├── development/     # Código en desarrollo (excluir)
│   ├── templates/
│   └── experiments/
└── mvp/            # Código MVP (excluir temporalmente)
    └── abu_dhabi_claims_mvp/
```

### Opción B: Usar archivos de marcado
```bash
# Crear archivo .quality-ignore en carpetas a excluir
echo "# Exclude from quality checks" > src/fhir_omop/etl/abu_dhabi_claims_mvp/.quality-ignore

# Luego usar en el workflow
find src/ -name "*.py" -not -path "*/.quality-ignore/*" -exec black --check {} \;
```

## 4. 🔧 Control interactivo para desarrollo local

### Script personalizado para testing
```python
#!/usr/bin/env python3
"""
Script para control interactivo de calidad de código
"""
import os
import subprocess
import sys
from pathlib import Path

def get_changed_files():
    """Obtener archivos cambiados desde el último commit"""
    result = subprocess.run(
        ["git", "diff", "--name-only", "HEAD~1", "--", "*.py"],
        capture_output=True, text=True
    )
    return result.stdout.strip().split('\n') if result.stdout else []

def get_files_by_pattern(pattern):
    """Obtener archivos por patrón"""
    return list(Path('.').rglob(pattern))

def main():
    print("🔍 Selector de archivos para análisis de calidad")
    print("1. Archivos modificados recientemente")
    print("2. Solo mappers")
    print("3. Solo core modules")
    print("4. Archivos específicos")
    print("5. Todo excepto templates")
    
    choice = input("Selecciona una opción (1-5): ")
    
    files = []
    if choice == "1":
        files = get_changed_files()
    elif choice == "2":
        files = list(Path('src/fhir_omop/mappers').rglob('*.py'))
    elif choice == "3":
        files = [
            'src/fhir_omop/config.py',
            'src/fhir_omop/main.py',
            *Path('src/fhir_omop/utils').rglob('*.py')
        ]
    elif choice == "4":
        files_input = input("Ingresa archivos separados por comas: ")
        files = [f.strip() for f in files_input.split(',')]
    elif choice == "5":
        files = [
            f for f in Path('src').rglob('*.py') 
            if 'abu_dhabi_claims_mvp' not in str(f) 
            and 'temp' not in str(f)
            and 'demo' not in str(f)
        ]
    
    if files:
        print(f"\\n📊 Analizando {len(files)} archivos...")
        for file in files:
            print(f"  - {file}")
        
        # Ejecutar herramientas
        subprocess.run(["black", "--check"] + [str(f) for f in files])
        subprocess.run(["isort", "--check-only"] + [str(f) for f in files])
        subprocess.run(["flake8"] + [str(f) for f in files])

if __name__ == "__main__":
    main()
```

## 5. 🎛️ Control por variables de entorno

### En el workflow
```yaml
env:
  QUALITY_SCOPE: "stable"  # stable, development, all
  EXCLUDE_PATTERNS: "abu_dhabi_claims_mvp,temp,demo"

steps:
- name: Set analysis scope
  run: |
    if [ "$QUALITY_SCOPE" = "stable" ]; then
      echo "ANALYSIS_PATHS=src/fhir_omop/mappers/ src/fhir_omop/utils/ tests/" >> $GITHUB_ENV
    elif [ "$QUALITY_SCOPE" = "development" ]; then
      echo "ANALYSIS_PATHS=src/fhir_omop/etl/abu_dhabi_claims_mvp/ demo/" >> $GITHUB_ENV
    else
      echo "ANALYSIS_PATHS=." >> $GITHUB_ENV
    fi

- name: Run Black with dynamic scope
  run: black --check $ANALYSIS_PATHS
```

## 6. 📋 Control por perfiles predefinidos

### Crear perfiles en pyproject.toml
```toml
# Perfil para desarrollo core
[tool.black.profiles.core]
include = '''/(src/fhir_omop/(config|main|mappers|utils)|tests)/'''

# Perfil para desarrollo completo
[tool.black.profiles.full]
include = '''\.py$'''
extend-exclude = '''/(temp|\.git)/'''

# Perfil para MVP
[tool.black.profiles.mvp]
include = '''/(src/fhir_omop/etl/abu_dhabi_claims_mvp|demo)/'''
```

## 7. 🚀 Comandos prácticos para uso inmediato

### Analizar solo código core
```bash
black --check src/fhir_omop/mappers/ src/fhir_omop/utils/ tests/
```

### Analizar archivos modificados hoy
```bash
find src/ -name "*.py" -newermt "1 day ago" -exec black --check {} \;
```

### Analizar excluyendo múltiples patrones
```bash
black --check --extend-exclude="/(abu_dhabi_claims_mvp|temp|demo|.*_template)/" src/
```

### Analizar solo archivos grandes (>100 líneas)
```bash
find src/ -name "*.py" -exec wc -l {} \; | awk '$1 > 100' | cut -d' ' -f2 | xargs black --check
```

## 8. 📊 Verificación de tu configuración actual

### Ver qué archivos se analizarían
```bash
# Con Black
black --check --verbose --extend-exclude="/(abu_dhabi_claims_mvp|temp|demo)/" src/ 2>&1 | grep "Found input"

# Con isort
isort --check-only --verbose --skip-glob="*/abu_dhabi_claims_mvp/*" src/

# Con flake8
flake8 --exclude="*/abu_dhabi_claims_mvp/*,*/temp/*" src/ --verbose
```
